import React, { useState, useEffect } from 'react';
import { Layout, Typography, Card, Button, Input, Select, DatePicker, Space, Tabs, Tooltip, Empty } from 'antd';
import { PlusOutlined, SearchOutlined, FilterOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import QuoteList from '../components/QuoteList';
import { getQuotes, deleteQuote } from '../services/quoteService';
import { message } from 'antd';

const { Content } = Layout;
const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * 报价管理页面
 * @returns {JSX.Element} - 渲染的页面
 */
const QuotesPage = () => {
  const [quotes, setQuotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: null,
  });
  const [activeTab, setActiveTab] = useState('all');

  // 获取报价列表
  useEffect(() => {
    fetchQuotes();
  }, [filters, activeTab]);

  // 获取报价数据
  const fetchQuotes = async () => {
    try {
      setLoading(true);

      // 构建查询参数
      const params = {};
      if (searchText) {
        params.search = searchText;
      }
      if (filters.status !== 'all') {
        params.status = filters.status;
      }
      if (filters.dateRange) {
        params.start_date = filters.dateRange[0].format('YYYY-MM-DD');
        params.end_date = filters.dateRange[1].format('YYYY-MM-DD');
      }

      // 根据活动标签添加额外参数
      if (activeTab === 'my') {
        params.created_by_me = true;
      }

      console.log(`正在请求报价数据 [标签: ${activeTab}]`, params);
      const data = await getQuotes(params);
      console.log(`报价数据响应 [标签: ${activeTab}]`, data);
      setQuotes(data || []);
    } catch (error) {
      console.error('获取报价列表失败:', error);
      message.error('获取报价列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = () => {
    fetchQuotes();
  };

  // 处理过滤器变化
  const handleFilterChange = (key, value) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  // 处理删除报价
  const handleDeleteQuote = async (id) => {
    try {
      await deleteQuote(id);
      message.success('删除报价成功');
      fetchQuotes();
    } catch (error) {
      console.error('删除报价失败:', error);
      message.error('删除报价失败');
    }
  };

  // 渲染过滤器
  const renderFilters = () => (
    <div className="flex flex-wrap gap-4 mb-4">
      <Input
        placeholder="搜索供应商名称"
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        onPressEnter={handleSearch}
        suffix={<SearchOutlined onClick={handleSearch} />}
        style={{ width: 200 }}
      />

      <Select
        placeholder="状态"
        value={filters.status}
        onChange={(value) => handleFilterChange('status', value)}
        style={{ width: 120 }}
      >
        <Option value="all">全部状态</Option>
        <Option value="pending">待处理</Option>
        <Option value="accepted">已接受</Option>
        <Option value="rejected">已拒绝</Option>
      </Select>

      <RangePicker
        placeholder={['开始日期', '结束日期']}
        value={filters.dateRange}
        onChange={(dates) => handleFilterChange('dateRange', dates)}
      />

      <Button
        type="primary"
        icon={<FilterOutlined />}
        onClick={handleSearch}
      >
        筛选
      </Button>
    </div>
  );

  return (
    <Content className="p-6">
      <div className="flex justify-between items-center mb-4">
        <div>
          <Title level={2}>报价管理</Title>
          <Typography.Text type="secondary">
            "全部报价"显示您有权查看的所有报价；"我的报价"显示您创建的任务收到的报价
          </Typography.Text>
        </div>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'all',
              label: <Tooltip title="显示您有权查看的所有报价">全部报价</Tooltip>
            },
            {
              key: 'my',
              label: <Tooltip title="显示您创建的任务收到的报价">我的报价</Tooltip>
            }
          ]}
        />

        {renderFilters()}

        {quotes.length === 0 && !loading ? (
          <Empty
            description={
              activeTab === 'my'
                ? '您创建的任务还没有收到报价'
                : '暂无报价数据'
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <QuoteList
            quotes={quotes}
            onDelete={handleDeleteQuote}
            loading={loading}
          />
        )}
      </Card>
    </Content>
  );
};

export default QuotesPage;
